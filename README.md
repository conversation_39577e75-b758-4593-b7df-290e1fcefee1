# NiceGui + Vue 3 Integration Demo

This project demonstrates how to embed an external Vue 3 Single Page Application (SPA) inside a Python NiceGui application, supporting both development and production workflows.

## 🏗️ Architecture

- **NiceGui Backend**: Python web application serving the main interface and static files
- **Vue 3 Frontend**: Independent SPA with TypeScript, Vite, and hot reload
- **Communication**: PostMessage API for Vue ↔ NiceGui interaction
- **Development**: Dual server setup (NiceGui:8000, Vue:5173)
- **Production**: Single server (NiceGui serves built Vue assets)

## 📋 Prerequisites

- **Python 3.11+** with pip
- **Node.js 18+** with npm
- **Git** (optional, for version control)

## 🚀 Quick Start

### 1. Python Setup

```bash
# Install NiceGui
pip install nicegui

# Optional: Install development dependencies
pip install pytest httpx ruff black
```

### 2. Vue Setup

```bash
# Navigate to Vue directory and install dependencies
cd vue
npm install
```

### 3. Development Workflow

**Option A: Dual Server (Recommended for development)**

```bash
# Terminal 1: Start Vue dev server
cd vue
npm run dev
# Vue will run on http://localhost:5173

# Terminal 2: Start NiceGui in development mode
DEV_MODE=true python main.py
# NiceGui will run on http://localhost:8000
```

**Option B: Single Server (Production-like)**

```bash
# Build Vue for production
cd vue
npm run build

# Start NiceGui (will serve built Vue assets)
python main.py
# Everything runs on http://localhost:8000
```

### 4. Access the Application

1. Open http://localhost:8000 in your browser
2. Navigate to "Embedded Vue App" or go directly to http://localhost:8000/embedded
3. The Vue SPA will load in an iframe with full viewport

## 🔧 Configuration

### Environment Variables

- `DEV_MODE=true`: Enables development mode (iframe points to Vue dev server)
- `VUE_DEV_URL=http://localhost:5173`: Vue development server URL
- `PORT=8000`: NiceGui server port

### File Structure

```
├── main.py                 # NiceGui application
├── vue/                    # Vue 3 application
│   ├── src/
│   │   ├── App.vue        # Main Vue component with postMessage
│   │   ├── views/
│   │   └── components/
│   ├── vite.config.ts     # Vite configuration (base: '/vueapp/')
│   └── package.json       # Vue dependencies and scripts
├── static/vue/            # Built Vue assets (production)
├── tests/                 # Python tests
└── README.md
```

## 🧪 Testing

### Unit Tests

```bash
# Run Python tests
pytest tests/test_routes.py -v

# Run Vue tests
cd vue
npm run test:unit
```

### Manual Testing

1. **Development Mode**: Verify hot reload works in both Vue and NiceGui
2. **Production Mode**: Ensure `/embedded` loads Vue SPA without errors
3. **Communication**: Test postMessage between Vue and NiceGui
4. **Responsive**: Check iframe fills viewport on different screen sizes

## 📦 Production Deployment

### Build Process

```bash
# 1. Build Vue application
cd vue
npm run build

# 2. Start NiceGui (serves built assets from static/vue/)
python main.py
```

### Docker Deployment (Optional)

```dockerfile
# Multi-stage build
FROM node:20-alpine AS vue-builder
WORKDIR /app/vue
COPY vue/package*.json ./
RUN npm ci
COPY vue/ ./
RUN npm run build

FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt ./
RUN pip install -r requirements.txt
COPY main.py ./
COPY --from=vue-builder /app/static/vue ./static/vue
EXPOSE 8000
CMD ["python", "main.py"]
```

## 🔍 Quality Gates

### Code Quality

```bash
# Python linting and formatting
ruff check .
black .

# Vue linting and formatting
cd vue
npm run lint
npm run format
```

### Health Checks

- **Health Endpoint**: GET `/health` returns application status
- **Vue Communication**: PostMessage API confirms Vue app loaded
- **Static Assets**: Verify built files exist in `static/vue/`

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure `base: '/vueapp/'` in `vite.config.ts`
2. **404 on Static Files**: Check Vue build output in `static/vue/`
3. **PostMessage Not Working**: Verify iframe src and origin policies
4. **Hot Reload Issues**: Ensure both servers running in development mode

### Debug Mode

```bash
# Enable verbose logging
DEBUG=true DEV_MODE=true python main.py
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run quality gates: `pytest`, `ruff`, `black`
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🔗 Resources

- [NiceGui Documentation](https://nicegui.io/)
- [Vue 3 Documentation](https://vuejs.org/)
- [Vite Documentation](https://vitejs.dev/)
- [PostMessage API](https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage)
