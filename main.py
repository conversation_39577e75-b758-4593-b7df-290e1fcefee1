"""
NiceGui application that embeds an external Vue 3 SPA.

This application demonstrates how to integrate a Vue.js single-page application
within a NiceGui Python web application, supporting both development and production modes.
"""

import os
from pathlib import Path
from typing import Optional

from nicegui import app, ui


# Configuration
DEV_MODE = os.getenv("DEV_MODE", "false").lower() == "true"
VUE_DEV_URL = os.getenv("VUE_DEV_URL", "http://localhost:5173")
STATIC_VUE_PATH = Path("static/vue")
VUE_ROUTE_PREFIX = "/vueapp"

# Serve Vue static files in production mode
if not DEV_MODE and STATIC_VUE_PATH.exists():
    app.add_static_files(VUE_ROUTE_PREFIX, str(STATIC_VUE_PATH))


@ui.page("/")
async def index_page():
    """Main landing page with navigation."""
    ui.page_title("NiceGui + Vue Integration")
    
    with ui.header().classes("items-center justify-between"):
        ui.label("NiceGui + Vue Demo").classes("text-h6")
        ui.button("Menu", icon="menu").props("flat").classes("lg:hidden")
    
    with ui.left_drawer().classes("bg-blue-100"):
        ui.nav_link("Home", "/")
        ui.nav_link("Embedded Vue App", "/embedded")
        ui.separator()
        ui.label(f"Mode: {'Development' if DEV_MODE else 'Production'}").classes("text-caption")
    
    with ui.column().classes("w-full max-w-2xl mx-auto p-4"):
        ui.markdown("""
        # NiceGui + Vue 3 Integration Demo
        
        This application demonstrates how to embed an external Vue 3 SPA 
        inside a NiceGui application.
        
        ## Features
        - **Development Mode**: Vue dev server with hot reload
        - **Production Mode**: Static files served by NiceGui
        - **Communication**: PostMessage API between Vue and NiceGui
        - **Responsive**: Full viewport iframe integration
        
        Click the button below to view the embedded Vue application.
        """)
        
        ui.button("Open Vue App", on_click=lambda: ui.open("/embedded")).props("color=primary size=lg")


@ui.page("/embedded")
async def embedded_page():
    """Page that embeds the Vue SPA in an iframe."""
    ui.page_title("Embedded Vue App")
    
    # Status indicator for Vue app communication
    status_label = ui.label("Loading Vue app...").classes("text-caption text-grey-6")
    
    # Determine iframe source based on mode
    iframe_src = VUE_DEV_URL if DEV_MODE else f"{VUE_ROUTE_PREFIX}/index.html"
    
    # Create iframe with full viewport
    iframe_html = f"""
    <iframe 
        id="vue-iframe"
        src="{iframe_src}" 
        style="border:none;width:100%;height:100vh;display:block;"
        title="Vue 3 Application">
    </iframe>
    """
    
    ui.html(iframe_html)
    
    # JavaScript to listen for postMessage from Vue app
    ui.run_javascript(f"""
    window.addEventListener('message', function(event) {{
        // Security: In production, verify event.origin
        if (event.data && event.data.ready) {{
            // Update status via NiceGui
            fetch('/api/vue-status', {{
                method: 'POST',
                headers: {{'Content-Type': 'application/json'}},
                body: JSON.stringify({{status: 'Vue app loaded successfully'}})
            }});
        }}
    }});
    """)
    
    # API endpoint to update status from JavaScript
    @app.post("/api/vue-status")
    async def update_vue_status(request):
        """API endpoint to receive status updates from Vue app."""
        try:
            data = await request.json()
            status_label.text = data.get("status", "Unknown status")
            return {"success": True}
        except Exception as e:
            return {"success": False, "error": str(e)}


@ui.page("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "mode": "development" if DEV_MODE else "production",
        "vue_static_exists": STATIC_VUE_PATH.exists() if not DEV_MODE else None
    }


if __name__ == "__main__":
    # Development server configuration
    ui.run(
        host="0.0.0.0",
        port=int(os.getenv("PORT", "8000")),
        reload=DEV_MODE,
        show=False,  # Don't auto-open browser
        title="NiceGui + Vue Integration"
    )
