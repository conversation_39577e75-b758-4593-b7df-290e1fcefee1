<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue 3 SPA - Embedded in NiceGui</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .app-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 4px solid #42b883;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }
        
        .info-item {
            background: #e9ecef;
            padding: 1rem;
            border-radius: 6px;
        }
        
        .info-item strong {
            color: #495057;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin: 0.5rem;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .demo-button:active {
            transform: translateY(0);
        }
        
        .time-display {
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            color: #6c757d;
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <div class="logo">V3</div>
            <h1>{{ title }}</h1>
            
            <div class="status-card">
                <h3>🎉 Vue 3 SPA Successfully Embedded!</h3>
                <p>This Vue application is running {{ isEmbedded ? 'embedded inside NiceGui' : 'standalone' }}</p>
            </div>
            
            <div class="info-grid">
                <div class="info-item">
                    <strong>📱 Environment</strong>
                    <div>{{ isEmbedded ? 'Embedded' : 'Standalone' }}</div>
                </div>
                <div class="info-item">
                    <strong>🖥️ Viewport</strong>
                    <div>{{ viewport.width }}×{{ viewport.height }}</div>
                </div>
                <div class="info-item">
                    <strong>⏰ Current Time</strong>
                    <div class="time-display">{{ currentTime }}</div>
                </div>
                <div class="info-item">
                    <strong>🚀 Framework</strong>
                    <div>Vue 3 + CDN</div>
                </div>
            </div>
            
            <div v-if="messageStatus" class="success-message">
                {{ messageStatus }}
            </div>
            
            <div>
                <button @click="sendMessageToParent" class="demo-button">
                    Send Message to NiceGui Parent
                </button>
                <button @click="updateTime" class="demo-button">
                    Update Time
                </button>
            </div>
            
            <div style="margin-top: 2rem; color: #6c757d; font-size: 0.9rem;">
                <p>Vue 3 + CDN | Embedded in NiceGui | PostMessage Communication</p>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    title: 'Hello from External Vue 3 SPA!',
                    currentTime: '',
                    isEmbedded: window.parent !== window,
                    viewport: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    messageStatus: ''
                }
            },
            mounted() {
                // Update time immediately and then every second
                this.updateTime();
                setInterval(this.updateTime, 1000);
                
                // Update viewport info on resize
                window.addEventListener('resize', this.updateViewport);
                
                // Notify parent window that Vue app is ready
                this.notifyParentReady();
            },
            methods: {
                updateTime() {
                    this.currentTime = new Date().toLocaleString();
                },
                updateViewport() {
                    this.viewport = {
                        width: window.innerWidth,
                        height: window.innerHeight
                    };
                },
                notifyParentReady() {
                    if (this.isEmbedded) {
                        window.parent.postMessage({
                            ready: true,
                            timestamp: new Date().toISOString(),
                            message: 'Vue 3 SPA loaded successfully'
                        }, '*');
                    }
                },
                sendMessageToParent() {
                    if (this.isEmbedded) {
                        window.parent.postMessage({
                            type: 'demo-interaction',
                            message: 'Hello from Vue! Button clicked at ' + new Date().toLocaleTimeString(),
                            timestamp: new Date().toISOString()
                        }, '*');
                        this.messageStatus = 'Message sent to NiceGui parent!';
                        setTimeout(() => {
                            this.messageStatus = '';
                        }, 3000);
                    } else {
                        this.messageStatus = 'Not running in embedded mode';
                        setTimeout(() => {
                            this.messageStatus = '';
                        }, 3000);
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
