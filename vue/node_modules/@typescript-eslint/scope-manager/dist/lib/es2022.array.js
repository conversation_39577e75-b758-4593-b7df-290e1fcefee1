"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2022_array = void 0;
const base_config_1 = require("./base-config");
exports.es2022_array = {
    libs: [],
    variables: [
        ['Array', base_config_1.TYPE],
        ['ReadonlyArray', base_config_1.TYPE],
        ['Int8Array', base_config_1.TYPE],
        ['Uint8Array', base_config_1.TYPE],
        ['Uint8ClampedArray', base_config_1.TYPE],
        ['Int16Array', base_config_1.TYPE],
        ['Uint16Array', base_config_1.TYPE],
        ['Int32Array', base_config_1.TYPE],
        ['Uint32Array', base_config_1.TYPE],
        ['Float32Array', base_config_1.TYPE],
        ['Float64Array', base_config_1.TYPE],
        ['BigInt64Array', base_config_1.TYPE],
        ['BigUint64Array', base_config_1.TYPE],
    ],
};
