{"name": "@typescript-eslint/utils", "version": "8.37.0", "description": "Utilities for working with TypeScript + ESLint together", "files": ["dist", "!*.tsbuil<PERSON><PERSON>", "package.json", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./ast-utils": {"types": "./dist/ast-utils/index.d.ts", "default": "./dist/ast-utils/index.js"}, "./eslint-utils": {"types": "./dist/eslint-utils/index.d.ts", "default": "./dist/eslint-utils/index.js"}, "./json-schema": {"types": "./dist/json-schema.d.ts", "default": "./dist/json-schema.js"}, "./ts-eslint": {"types": "./dist/ts-eslint/index.d.ts", "default": "./dist/ts-eslint/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/utils"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io/packages/utils", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/scope-manager": "8.37.0", "@typescript-eslint/types": "8.37.0", "@typescript-eslint/typescript-estree": "8.37.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}, "devDependencies": {"@vitest/coverage-v8": "^3.1.3", "eslint": "*", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "nx": {"name": "utils", "includedScripts": ["clean"], "targets": {"lint": {"command": "eslint"}, "typecheck": {"outputs": ["{workspaceRoot}/dist", "{projectRoot}/dist"]}, "test": {"dependsOn": ["^build", "typecheck"]}}}}