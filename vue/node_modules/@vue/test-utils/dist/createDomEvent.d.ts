import { DomEventNameWithModifier, KeyName, keyCodesByKeyName } from './constants/dom-events';
interface TriggerOptions {
    code?: String;
    key?: String;
    keyCode?: Number;
    [custom: string]: any;
}
declare function createDOMEvent(eventString: DomEventNameWithModifier | string, options?: TriggerOptions): Event & TriggerOptions;
export { TriggerOptions, createDOMEvent, keyCodesByKeyName, KeyName };
