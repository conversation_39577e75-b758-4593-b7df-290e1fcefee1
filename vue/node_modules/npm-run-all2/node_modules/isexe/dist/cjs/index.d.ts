import * as posix from './posix.js';
import * as win32 from './win32.js';
export * from './options.js';
export { win32, posix };
/**
 * Determine whether a path is executable on the current platform.
 */
export declare const isexe: (path: string, options?: import("./options.js").IsexeOptions) => Promise<boolean>;
/**
 * Synchronously determine whether a path is executable on the
 * current platform.
 */
export declare const sync: (path: string, options?: import("./options.js").IsexeOptions) => boolean;
//# sourceMappingURL=index.d.ts.map