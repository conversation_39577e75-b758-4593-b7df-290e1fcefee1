import{d as m,C as d,V as p,i as f,R as x,c as v,e as _,a as g,u as e,o as i,b as o,x as b,t as T,W as k,T as y}from"./index-Dwf2ZVS8.js";/* empty css              */const B={key:0,flex:"~ col","h-full":"","items-center":"","justify-center":""},C={flex:"~ col gap2",mxa:"","items-center":""},I={"text-xl":""},R={"text-rose":""},N=m({__name:"custom-inspector-tab-view",setup(V){const a=x(),u=y(),n=d(!1),c=p(),r=f(()=>c.value.find(s=>s.name===a.params.name)?.pluginId);function l(){n.value=!0;const s=setTimeout(()=>{clearTimeout(s),u.replace("/overview")},2e3)}return(s,t)=>e(n)?(i(),v("div",B,[o("div",C,[t[1]||(t[1]=o("div",{"i-carbon-queued":"",mb2:"","text-5xl":"",op50:""},null,-1)),o("p",I,[o("code",R,T(e(a).params.name),1),t[0]||(t[0]=b(" not found "))]),t[2]||(t[2]=o("p",{mt8:"","animate-pulse":""}," Redirecting to overview page... ",-1))])])):!e(n)&&e(r)?(i(),_(e(k),{key:1,id:e(a).params.name,"plugin-id":e(r),onLoadError:l},null,8,["id","plugin-id"])):g("",!0)}});export{N as default};
