<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import { onMounted } from 'vue'

// Notify parent window (NiceGui) that Vue app is ready
onMounted(() => {
  // Send ready message to parent window
  if (window.parent && window.parent !== window) {
    window.parent.postMessage({ ready: true, timestamp: new Date().toISOString() }, '*')
  }
})
</script>

<template>
  <div id="app">
    <header class="app-header">
      <div class="logo-section">
        <img alt="Vue logo" class="logo" src="@/assets/logo.svg" width="60" height="60" />
        <h1>Hello from External Vue 3 SPA!</h1>
      </div>

      <nav class="main-nav">
        <RouterLink to="/" class="nav-link">Home</RouterLink>
        <RouterLink to="/about" class="nav-link">About</RouterLink>
      </nav>
    </header>

    <main class="main-content">
      <RouterView />
    </main>

    <footer class="app-footer">
      <p>Vue 3 + Vite + TypeScript | Embedded in NiceGui</p>
    </footer>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.logo {
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.logo-section h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.main-nav {
  display: flex;
  gap: 1rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.router-link-exact-active {
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.app-footer {
  background-color: #f8f9fa;
  padding: 1rem;
  text-align: center;
  color: #6c757d;
  font-size: 0.875rem;
  border-top: 1px solid #e9ecef;
}

@media (max-width: 768px) {
  .logo-section {
    flex-direction: column;
    text-align: center;
  }

  .logo-section h1 {
    font-size: 1.25rem;
  }

  .main-nav {
    justify-content: center;
  }

  .main-content {
    padding: 1rem;
  }
}
</style>
