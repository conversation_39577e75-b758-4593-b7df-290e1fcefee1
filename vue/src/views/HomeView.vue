<script setup lang="ts">
import { ref, onMounted } from 'vue'

const currentTime = ref('')
const embedInfo = ref({
  userAgent: navigator.userAgent,
  viewport: { width: window.innerWidth, height: window.innerHeight },
  isEmbedded: window.parent !== window
})

onMounted(() => {
  // Update time every second
  const updateTime = () => {
    currentTime.value = new Date().toLocaleString()
  }
  updateTime()
  setInterval(updateTime, 1000)

  // Update viewport info on resize
  const updateViewport = () => {
    embedInfo.value.viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    }
  }
  window.addEventListener('resize', updateViewport)
})
</script>

<template>
  <div class="home-view">
    <div class="hero-section">
      <h2>🎉 Vue 3 SPA Successfully Embedded!</h2>
      <p class="lead">
        This Vue application is running {{ embedInfo.isEmbedded ? 'embedded inside NiceGui' : 'standalone' }}
      </p>
    </div>

    <div class="info-cards">
      <div class="card">
        <h3>📱 Environment Info</h3>
        <ul>
          <li><strong>Embedded:</strong> {{ embedInfo.isEmbedded ? 'Yes' : 'No' }}</li>
          <li><strong>Viewport:</strong> {{ embedInfo.viewport.width }}×{{ embedInfo.viewport.height }}</li>
          <li><strong>Current Time:</strong> {{ currentTime }}</li>
        </ul>
      </div>

      <div class="card">
        <h3>🚀 Features</h3>
        <ul>
          <li>✅ Vue 3 + TypeScript + Vite</li>
          <li>✅ Hot reload in development</li>
          <li>✅ PostMessage communication</li>
          <li>✅ Responsive design</li>
          <li>✅ Production-ready build</li>
        </ul>
      </div>

      <div class="card">
        <h3>🔧 Technical Stack</h3>
        <ul>
          <li><strong>Frontend:</strong> Vue 3, TypeScript, Vite</li>
          <li><strong>Backend:</strong> Python NiceGui</li>
          <li><strong>Communication:</strong> PostMessage API</li>
          <li><strong>Deployment:</strong> Static files served by NiceGui</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h3>🎮 Interactive Demo</h3>
      <p>Try interacting with this Vue app while embedded in NiceGui:</p>
      <button @click="sendMessageToParent" class="demo-button">
        Send Message to NiceGui Parent
      </button>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  methods: {
    sendMessageToParent() {
      if (window.parent && window.parent !== window) {
        window.parent.postMessage({
          type: 'demo-interaction',
          message: 'Hello from Vue! Button clicked at ' + new Date().toLocaleTimeString(),
          timestamp: new Date().toISOString()
        }, '*')
        alert('Message sent to NiceGui parent!')
      } else {
        alert('Not running in embedded mode')
      }
    }
  }
}
</script>

<style scoped>
.home-view {
  max-width: 1000px;
  margin: 0 auto;
}

.hero-section {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.hero-section h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 2rem;
}

.lead {
  font-size: 1.2rem;
  color: #5a6c7d;
  margin: 0;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.card li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.card li:last-child {
  border-bottom: none;
}

.demo-section {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.demo-section h3 {
  color: #495057;
  margin-bottom: 1rem;
}

.demo-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.demo-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.demo-button:active {
  transform: translateY(0);
}

@media (max-width: 768px) {
  .hero-section h2 {
    font-size: 1.5rem;
  }

  .lead {
    font-size: 1rem;
  }

  .info-cards {
    grid-template-columns: 1fr;
  }
}
</style>
</script>
